#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
20 Mağaza üçün Detallı Maliyyə Hesabatı Excel Faylının Yaradılması
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils import get_column_letter
import os

def create_excel_dashboard():
    """Excel Dashboard faylını yaradır"""
    
    # Yeni workbook yaradırıq
    wb = Workbook()
    
    # Default sheet-i silirik
    wb.remove(wb.active)
    
    # 3 səhifə yaradırıq
    ws_settings = wb.create_sheet("⚙️ Ayarlar")
    ws_data_entry = wb.create_sheet("✍️ Məlumatların Daxil Edilməsi")
    ws_dashboard = wb.create_sheet("📊 Dashboard")
    
    # Rəng və stil təyinatları
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    subheader_fill = PatternFill(start_color="B4C6E7", end_color="B4C6E7", fill_type="solid")
    total_fill = PatternFill(start_color="FFC000", end_color="FFC000", fill_type="solid")
    
    header_font = Font(bold=True, color="FFFFFF")
    subheader_font = Font(bold=True)
    total_font = Font(bold=True)
    
    center_alignment = Alignment(horizontal="center", vertical="center")
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 1. AYARLAR SƏHİFƏSİ
    print("Ayarlar səhifəsi yaradılır...")
    
    # Mağaza adları
    ws_settings['A1'] = "Mağaza Adları"
    ws_settings['A1'].font = header_font
    ws_settings['A1'].fill = header_fill
    
    for i in range(1, 21):
        ws_settings[f'A{i+1}'] = f"Mağaza {i}"
    
    # Əməliyyat növləri
    ws_settings['B1'] = "Əməliyyat Növləri"
    ws_settings['B1'].font = header_font
    ws_settings['B1'].fill = header_fill
    
    operations = ["Satış", "Daxilolma", "Xərc"]
    for i, op in enumerate(operations, 2):
        ws_settings[f'B{i}'] = op
    
    # Ödəmə növləri
    ws_settings['C1'] = "Ödəmə Növləri"
    ws_settings['C1'].font = header_font
    ws_settings['C1'].fill = header_fill
    
    payment_types = ["Nağd", "ABB", "Kapital", "M10", "Umico"]
    for i, payment in enumerate(payment_types, 2):
        ws_settings[f'C{i}'] = payment
    
    # Sütun genişliklərini təyin edirik
    ws_settings.column_dimensions['A'].width = 15
    ws_settings.column_dimensions['B'].width = 15
    ws_settings.column_dimensions['C'].width = 15
    
    # 2. MƏLUMAT DAXİL EDİLMƏSİ SƏHİFƏSİ
    print("Məlumat daxil edilməsi səhifəsi yaradılır...")
    
    # Başlıqlar
    headers = ["Tarix", "Mağaza Adı", "Əməliyyat Növü", "Ödəmə Növü", "Məbləğ"]
    for i, header in enumerate(headers, 1):
        cell = ws_data_entry.cell(row=1, column=i, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Sütun genişliklərini təyin edirik
    column_widths = [12, 15, 15, 15, 12]
    for i, width in enumerate(column_widths, 1):
        ws_data_entry.column_dimensions[get_column_letter(i)].width = width
    
    # Data validation əlavə edirik
    # Mağaza adları üçün
    store_validation = DataValidation(type="list", formula1="Ayarlar!$A$2:$A$21")
    ws_data_entry.add_data_validation(store_validation)
    store_validation.add("B2:********")
    
    # Əməliyyat növləri üçün
    operation_validation = DataValidation(type="list", formula1="Ayarlar!$B$2:$B$4")
    ws_data_entry.add_data_validation(operation_validation)
    operation_validation.add("C2:********")
    
    # Ödəmə növləri üçün
    payment_validation = DataValidation(type="list", formula1="Ayarlar!$C$2:$C$6")
    ws_data_entry.add_data_validation(payment_validation)
    payment_validation.add("D2:********")
    
    # 3. DASHBOARD SƏHİFƏSİ
    print("Dashboard səhifəsi yaradılır...")
    
    # Mağaza adlarını A sütununa əlavə edirik
    ws_dashboard['A2'] = "Mağaza"
    ws_dashboard['A2'].font = header_font
    ws_dashboard['A2'].fill = header_fill
    ws_dashboard['A2'].alignment = center_alignment
    
    for i in range(1, 21):
        ws_dashboard[f'A{i+2}'] = f"Mağaza {i}"
    
    # Multi-level başlıqlar yaradırıq
    # 1-ci səviyyə başlıqlar (birləşdirilmiş)
    ws_dashboard.merge_cells('B1:G1')
    ws_dashboard['B1'] = "Satış"
    ws_dashboard['B1'].font = header_font
    ws_dashboard['B1'].fill = header_fill
    ws_dashboard['B1'].alignment = center_alignment
    
    ws_dashboard.merge_cells('H1:M1')
    ws_dashboard['H1'] = "Daxilolma"
    ws_dashboard['H1'].font = header_font
    ws_dashboard['H1'].fill = header_fill
    ws_dashboard['H1'].alignment = center_alignment
    
    ws_dashboard.merge_cells('N1:S1')
    ws_dashboard['N1'] = "Xərc"
    ws_dashboard['N1'].font = header_font
    ws_dashboard['N1'].fill = header_fill
    ws_dashboard['N1'].alignment = center_alignment
    
    ws_dashboard.merge_cells('T1:U1')
    ws_dashboard['T1'] = "Yekun Göstəricilər"
    ws_dashboard['T1'].font = header_font
    ws_dashboard['T1'].fill = header_fill
    ws_dashboard['T1'].alignment = center_alignment
    
    # 2-ci səviyyə başlıqlar
    payment_headers = ["Nağd", "ABB", "Kapital", "M10", "Umico", "Cəmi"]
    
    # Satış başlıqları (B2:G2)
    for i, header in enumerate(payment_headers, 2):
        cell = ws_dashboard.cell(row=2, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Daxilolma başlıqları (H2:M2)
    for i, header in enumerate(payment_headers, 8):
        cell = ws_dashboard.cell(row=2, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Xərc başlıqları (N2:S2)
    for i, header in enumerate(payment_headers, 14):
        cell = ws_dashboard.cell(row=2, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Yekun göstəricilər başlıqları
    ws_dashboard['T2'] = "Satış-Daxil. Fərqi"
    ws_dashboard['T2'].font = subheader_font
    ws_dashboard['T2'].fill = subheader_fill
    ws_dashboard['T2'].alignment = center_alignment
    ws_dashboard['T2'].border = thin_border
    
    ws_dashboard['U2'] = "Qalıq (Mənf./Zərər)"
    ws_dashboard['U2'].font = subheader_font
    ws_dashboard['U2'].fill = subheader_fill
    ws_dashboard['U2'].alignment = center_alignment
    ws_dashboard['U2'].border = thin_border
    
    # Sütun genişliklərini təyin edirik
    ws_dashboard.column_dimensions['A'].width = 12
    for col in range(2, 22):  # B-dən U-ya qədər
        ws_dashboard.column_dimensions[get_column_letter(col)].width = 10

    # FORMULLAR ƏLAVƏ EDİRİK
    print("Formullar əlavə edilir...")

    # Hər mağaza üçün formullar yaradırıq
    for row in range(3, 23):  # 3-cü sətirdən 22-ci sətrə qədər (20 mağaza)
        store_cell = f"$A{row}"

        # Satış formulları (B3:F22)
        payment_types_row2 = ["Nağd", "ABB", "Kapital", "M10", "Umico"]
        for col_idx, payment_type in enumerate(payment_types_row2, 2):
            col_letter = get_column_letter(col_idx)
            formula = f'=SUMIFS(tbl_Data[Məbləğ], tbl_Data[Mağaza Adı], {store_cell}, tbl_Data[Əməliyyat Növü], "Satış", tbl_Data[Ödəmə Növü], {col_letter}$2)'
            ws_dashboard[f'{col_letter}{row}'] = formula

        # Satış cəmi (G sütunu)
        ws_dashboard[f'G{row}'] = f'=SUM(B{row}:F{row})'
        ws_dashboard[f'G{row}'].fill = total_fill
        ws_dashboard[f'G{row}'].font = total_font

        # Daxilolma formulları (H3:L22)
        for col_idx, payment_type in enumerate(payment_types_row2, 8):
            col_letter = get_column_letter(col_idx)
            formula = f'=SUMIFS(tbl_Data[Məbləğ], tbl_Data[Mağaza Adı], {store_cell}, tbl_Data[Əməliyyat Növü], "Daxilolma", tbl_Data[Ödəmə Növü], {col_letter}$2)'
            ws_dashboard[f'{col_letter}{row}'] = formula

        # Daxilolma cəmi (M sütunu)
        ws_dashboard[f'M{row}'] = f'=SUM(H{row}:L{row})'
        ws_dashboard[f'M{row}'].fill = total_fill
        ws_dashboard[f'M{row}'].font = total_font

        # Xərc formulları (N3:R22)
        for col_idx, payment_type in enumerate(payment_types_row2, 14):
            col_letter = get_column_letter(col_idx)
            formula = f'=SUMIFS(tbl_Data[Məbləğ], tbl_Data[Mağaza Adı], {store_cell}, tbl_Data[Əməliyyat Növü], "Xərc", tbl_Data[Ödəmə Növü], {col_letter}$2)'
            ws_dashboard[f'{col_letter}{row}'] = formula

        # Xərc cəmi (S sütunu)
        ws_dashboard[f'S{row}'] = f'=SUM(N{row}:R{row})'
        ws_dashboard[f'S{row}'].fill = total_fill
        ws_dashboard[f'S{row}'].font = total_font

        # Satış-Daxilolma Fərqi (T sütunu)
        ws_dashboard[f'T{row}'] = f'=G{row}-M{row}'
        ws_dashboard[f'T{row}'].fill = total_fill
        ws_dashboard[f'T{row}'].font = total_font

        # Qalıq (U sütunu)
        ws_dashboard[f'U{row}'] = f'=M{row}-S{row}'
        ws_dashboard[f'U{row}'].fill = total_fill
        ws_dashboard[f'U{row}'].font = total_font

    # Bütün məbləğ xanalarını valyuta formatına çeviririk
    print("Valyuta formatı tətbiq edilir...")
    for row in range(3, 23):
        for col in range(2, 22):  # B-dən U-ya qədər
            cell = ws_dashboard.cell(row=row, column=col)
            cell.number_format = '#,##0.00 "₼"'
            cell.border = thin_border

    # Məlumat daxil edilməsi səhifəsində cədvəl yaradırıq
    print("Məlumat cədvəli yaradılır...")

    # Cədvəl üçün nümunə məlumat əlavə edirik
    sample_data = [
        ["2024-01-15", "Mağaza 1", "Satış", "Nağd", 1500],
        ["2024-01-15", "Mağaza 1", "Satış", "ABB", 2000],
        ["2024-01-16", "Mağaza 2", "Daxilolma", "Kapital", 5000],
        ["2024-01-16", "Mağaza 3", "Xərc", "Nağd", 300],
    ]

    for row_idx, row_data in enumerate(sample_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            ws_data_entry.cell(row=row_idx, column=col_idx, value=value)

    # Tarix sütununu tarix formatına çeviririk
    for row in range(2, 1000):  # Gələcək məlumatlar üçün
        ws_data_entry[f'A{row}'].number_format = 'DD/MM/YYYY'

    # Məbləğ sütununu valyuta formatına çeviririk
    for row in range(2, 1000):
        ws_data_entry[f'E{row}'].number_format = '#,##0.00 "₼"'

    # Faylı yadda saxlayırıq
    filename = "Detalli_Magaza_Dashboard.xlsx"
    wb.save(filename)
    print(f"Excel faylı yaradıldı: {filename}")

    return filename

if __name__ == "__main__":
    create_excel_dashboard()
