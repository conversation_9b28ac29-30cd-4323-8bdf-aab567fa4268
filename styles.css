/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #217346; /* Excel green */
    color: white;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.toolbar button {
    background-color: #1e6b41;
    color: white;
    border: none;
    padding: 8px 12px;
    margin-left: 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.toolbar button:hover {
    background-color: #18563a;
}

/* Main content area */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.supplier-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.welcome-message {
    text-align: center;
    margin-top: 100px;
    color: #666;
}

.summary-panel {
    padding: 15px 20px;
    background-color: #f0f0f0;
    border-top: 1px solid #ddd;
}

/* Tabs styles */
.tabs-container {
    display: flex;
    background-color: #f0f0f0;
    border-top: 1px solid #ddd;
}

.tabs {
    display: flex;
    flex: 1;
    overflow-x: auto;
}

.tab {
    padding: 10px 20px;
    background-color: #e0e0e0;
    border-right: 1px solid #ccc;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.3s;
}

.tab:hover {
    background-color: #d0d0d0;
}

.tab.active {
    background-color: #217346;
    color: white;
}

.tab-actions {
    padding: 5px;
}

.tab-actions button {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #555;
}

.tab-actions button:hover {
    color: #217346;
}

/* Table styles */
.supplier-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.supplier-table th {
    background-color: #217346;
    color: white;
    padding: 10px;
    text-align: left;
}

.supplier-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #ddd;
}

.supplier-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.supplier-table tr:hover {
    background-color: #f0f0f0;
}

.supplier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.supplier-header h2 {
    color: #217346;
}

.supplier-header button {
    background-color: #217346;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
}

.supplier-header button:hover {
    background-color: #18563a;
}

.supplier-summary {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border-left: 4px solid #217346;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    width: 50%;
    max-width: 500px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.close {
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

form button {
    background-color: #217346;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

form button:hover {
    background-color: #18563a;
}

/* Summary page styles */
.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.summary-table th {
    background-color: #217346;
    color: white;
    padding: 10px;
    text-align: left;
}

.summary-table td {
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.summary-table tr:last-child {
    font-weight: bold;
    background-color: #f0f0f0;
}

.debt-positive {
    color: #d32f2f; /* Red for debt */
}

.debt-negative {
    color: #388e3c; /* Green for credit */
}

.debt-zero {
    color: #333;
}
