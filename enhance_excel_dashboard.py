#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel Dashboard-ı təkmilləşdirmək üçün əlavə funksionallıq
"""

from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule
from openpyxl.worksheet.table import Table, TableStyleInfo
import os

def enhance_excel_dashboard():
    """Excel Dashboard-ı təkmilləşdirir"""
    
    filename = "Detalli_Magaza_Dashboard.xlsx"
    
    if not os.path.exists(filename):
        print(f"Fayl tapılmadı: {filename}")
        return
    
    # Faylı açırıq
    wb = load_workbook(filename)
    
    # Səhifələri alırıq
    ws_data_entry = wb["✍️ Məlumatların Daxil Edilməsi"]
    ws_dashboard = wb["📊 Dashboard"]
    
    print("Excel faylı təkmilləşdirilir...")
    
    # 1. <PERSON>əlumat daxil edilməsi səhifəsində cədvəl yaradırıq
    print("Cədvəl (Table) yaradılır...")
    
    # Cədvəl aralığını təyin edirik (A1:E1000)
    table_range = "A1:E1000"
    
    # Cədvəl yaradırıq
    table = Table(displayName="tbl_Data", ref=table_range)
    
    # Cədvəl stilini təyin edirik
    style = TableStyleInfo(
        name="TableStyleMedium9", 
        showFirstColumn=False,
        showLastColumn=False, 
        showRowStripes=True, 
        showColumnStripes=True
    )
    table.tableStyleInfo = style
    
    # Cədvəli səhifəyə əlavə edirik
    ws_data_entry.add_table(table)
    
    # 2. Dashboard səhifəsində şərti formatlamanı əlavə edirik
    print("Şərti formatlama əlavə edilir...")
    
    # Qalıq sütunu üçün şərti formatlama (U sütunu)
    # Müsbət dəyərlər yaşıl, mənfi dəyərlər qırmızı
    green_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
    red_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
    
    # Müsbət dəyərlər üçün qayda
    positive_rule = CellIsRule(
        operator='greaterThan',
        formula=['0'],
        fill=green_fill
    )
    
    # Mənfi dəyərlər üçün qayda
    negative_rule = CellIsRule(
        operator='lessThan',
        formula=['0'],
        fill=red_fill
    )
    
    # Qalıq sütununa (U3:U22) şərti formatlamanı tətbiq edirik
    ws_dashboard.conditional_formatting.add("U3:U22", positive_rule)
    ws_dashboard.conditional_formatting.add("U3:U22", negative_rule)
    
    # 3. Satış-Daxilolma Fərqi sütunu üçün də şərti formatlama
    ws_dashboard.conditional_formatting.add("T3:T22", positive_rule)
    ws_dashboard.conditional_formatting.add("T3:T22", negative_rule)
    
    # 4. Dashboard səhifəsində yekun sətir əlavə edirik
    print("Yekun sətir əlavə edilir...")
    
    # 23-cü sətirdə yekun hesablamalar
    ws_dashboard['A23'] = "YEKUN"
    ws_dashboard['A23'].font = Font(bold=True, size=12)
    ws_dashboard['A23'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    ws_dashboard['A23'].font = Font(bold=True, color="FFFFFF")
    
    # Yekun formulları
    for col in range(2, 22):  # B-dən U-ya qədər
        col_letter = chr(65 + col - 1)  # A=65, B=66, ...
        if col_letter in ['G', 'M', 'S', 'T', 'U']:  # Cəmi sütunları
            ws_dashboard[f'{col_letter}23'] = f'=SUM({col_letter}3:{col_letter}22)'
        else:  # Digər sütunlar
            ws_dashboard[f'{col_letter}23'] = f'=SUM({col_letter}3:{col_letter}22)'
        
        # Yekun sətirinin formatı
        ws_dashboard[f'{col_letter}23'].font = Font(bold=True, color="FFFFFF")
        ws_dashboard[f'{col_letter}23'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        ws_dashboard[f'{col_letter}23'].number_format = '#,##0.00 "₼"'
    
    # 5. Səhifə qorunması
    print("Səhifə qorunması tətbiq edilir...")
    
    # Dashboard səhifəsini qoruyuruq
    ws_dashboard.protection.sheet = True
    ws_dashboard.protection.password = "dashboard123"
    
    # Ayarlar səhifəsini də qoruyuruq
    ws_settings = wb["⚙️ Ayarlar"]
    ws_settings.protection.sheet = True
    ws_settings.protection.password = "settings123"
    
    # 6. Səhifə başlıqları və açıqlamalar əlavə edirik
    print("Başlıqlar və açıqlamalar əlavə edilir...")
    
    # Dashboard səhifəsinə başlıq əlavə edirik
    ws_dashboard.insert_rows(1)
    ws_dashboard.merge_cells('A1:U1')
    ws_dashboard['A1'] = "20 MAĞAZA ÜÇÜN DETALLI MALİYYƏ HESABATI DASHBOARD"
    ws_dashboard['A1'].font = Font(bold=True, size=16, color="FFFFFF")
    ws_dashboard['A1'].fill = PatternFill(start_color="1F4E79", end_color="1F4E79", fill_type="solid")
    ws_dashboard['A1'].alignment = Alignment(horizontal="center", vertical="center")
    
    # Bütün formulları yenidən təyin edirik (sətir əlavə etdiyimiz üçün)
    print("Formullar yenilənir...")
    
    # Mağaza adlarını yenidən yazırıq
    ws_dashboard['A3'] = "Mağaza"
    for i in range(1, 21):
        ws_dashboard[f'A{i+3}'] = f"Mağaza {i}"
    
    # Başlıqları yenidən təyin edirik
    # 2-ci səviyyə başlıqlar (əvvəlki 1-ci səviyyə indi 2-ci səviyyə oldu)
    ws_dashboard.merge_cells('B2:G2')
    ws_dashboard['B2'] = "Satış"
    
    ws_dashboard.merge_cells('H2:M2')
    ws_dashboard['H2'] = "Daxilolma"
    
    ws_dashboard.merge_cells('N2:S2')
    ws_dashboard['N2'] = "Xərc"
    
    ws_dashboard.merge_cells('T2:U2')
    ws_dashboard['T2'] = "Yekun Göstəricilər"
    
    # 3-cü səviyyə başlıqlar
    payment_headers = ["Nağd", "ABB", "Kapital", "M10", "Umico", "Cəmi"]
    
    for i, header in enumerate(payment_headers, 2):
        ws_dashboard.cell(row=3, column=i, value=header)
        ws_dashboard.cell(row=3, column=i+6, value=header)  # Daxilolma
        ws_dashboard.cell(row=3, column=i+12, value=header)  # Xərc
    
    ws_dashboard['T3'] = "Satış-Daxil. Fərqi"
    ws_dashboard['U3'] = "Qalıq (Mənf./Zərər)"
    
    # Faylı yadda saxlayırıq
    wb.save(filename)
    print(f"Excel faylı təkmilləşdirildi: {filename}")
    
    # Fayl haqqında məlumat
    print("\n" + "="*60)
    print("EXCEL FAYLI HAZIRDIR!")
    print("="*60)
    print(f"Fayl adı: {filename}")
    print(f"Fayl ölçüsü: {os.path.getsize(filename)} bayt")
    print("\nSəhifələr:")
    print("1. 📊 Dashboard - Əsas hesabat səhifəsi")
    print("2. ✍️ Məlumatların Daxil Edilməsi - Məlumat daxil etmək üçün")
    print("3. ⚙️ Ayarlar - Dropdown siyahıların mənbəyi")
    print("\nXüsusiyyətlər:")
    print("✓ 20 mağaza üçün tam avtomatik hesablamalar")
    print("✓ Satış, Daxilolma, Xərc əməliyyatları")
    print("✓ 5 ödəmə növü (Nağd, ABB, Kapital, M10, Umico)")
    print("✓ Avtomatik cəmlər və qalıq hesablamaları")
    print("✓ Şərti formatlama (yaşıl/qırmızı rənglər)")
    print("✓ Data validation (dropdown siyahılar)")
    print("✓ Səhifə qorunması")
    print("✓ Valyuta formatı (₼)")
    print("="*60)

if __name__ == "__main__":
    enhance_excel_dashboard()
