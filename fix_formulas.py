#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel Dashboard formullarını düzəltmək
"""

from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import os

def fix_formulas():
    """Excel Dashboard formullarını düzəldir"""
    
    filename = "Detalli_Magaza_Dashboard.xlsx"
    
    if not os.path.exists(filename):
        print(f"Fayl tapılmadı: {filename}")
        return
    
    # Faylı açırıq
    wb = load_workbook(filename)
    ws_dashboard = wb["📊 Dashboard"]
    
    print("Formullar düzəldilir...")
    
    # Stil təyinatları
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    subheader_fill = PatternFill(start_color="B4C6E7", end_color="B4C6E7", fill_type="solid")
    total_fill = PatternFill(start_color="FFC000", end_color="FFC000", fill_type="solid")
    
    header_font = Font(bold=True, color="FFFFFF")
    subheader_font = Font(bold=True)
    total_font = Font(bold=True)
    
    center_alignment = Alignment(horizontal="center", vertical="center")
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Başlıqları yenidən düzgün təyin edirik
    # 2-ci sətir - əsas qruplar
    ws_dashboard.merge_cells('B2:G2')
    ws_dashboard['B2'] = "Satış"
    ws_dashboard['B2'].font = header_font
    ws_dashboard['B2'].fill = header_fill
    ws_dashboard['B2'].alignment = center_alignment
    
    ws_dashboard.merge_cells('H2:M2')
    ws_dashboard['H2'] = "Daxilolma"
    ws_dashboard['H2'].font = header_font
    ws_dashboard['H2'].fill = header_fill
    ws_dashboard['H2'].alignment = center_alignment
    
    ws_dashboard.merge_cells('N2:S2')
    ws_dashboard['N2'] = "Xərc"
    ws_dashboard['N2'].font = header_font
    ws_dashboard['N2'].fill = header_fill
    ws_dashboard['N2'].alignment = center_alignment
    
    ws_dashboard.merge_cells('T2:U2')
    ws_dashboard['T2'] = "Yekun Göstəricilər"
    ws_dashboard['T2'].font = header_font
    ws_dashboard['T2'].fill = header_fill
    ws_dashboard['T2'].alignment = center_alignment
    
    # 3-cü sətir - detallı başlıqlar
    payment_headers = ["Nağd", "ABB", "Kapital", "M10", "Umico", "Cəmi"]
    
    # Satış başlıqları (B3:G3)
    for i, header in enumerate(payment_headers, 2):
        cell = ws_dashboard.cell(row=3, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Daxilolma başlıqları (H3:M3)
    for i, header in enumerate(payment_headers, 8):
        cell = ws_dashboard.cell(row=3, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Xərc başlıqları (N3:S3)
    for i, header in enumerate(payment_headers, 14):
        cell = ws_dashboard.cell(row=3, column=i, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = center_alignment
        cell.border = thin_border
    
    # Yekun göstəricilər başlıqları
    ws_dashboard['T3'] = "Satış-Daxil. Fərqi"
    ws_dashboard['T3'].font = subheader_font
    ws_dashboard['T3'].fill = subheader_fill
    ws_dashboard['T3'].alignment = center_alignment
    ws_dashboard['T3'].border = thin_border
    
    ws_dashboard['U3'] = "Qalıq (Mənf./Zərər)"
    ws_dashboard['U3'].font = subheader_font
    ws_dashboard['U3'].fill = subheader_fill
    ws_dashboard['U3'].alignment = center_alignment
    ws_dashboard['U3'].border = thin_border
    
    # Mağaza adlarını yenidən yazırıq
    ws_dashboard['A3'] = "Mağaza"
    ws_dashboard['A3'].font = header_font
    ws_dashboard['A3'].fill = header_fill
    ws_dashboard['A3'].alignment = center_alignment
    
    for i in range(1, 21):
        ws_dashboard[f'A{i+3}'] = f"Mağaza {i}"
    
    # FORMULLAR ƏLAVƏ EDİRİK (4-cü sətirdən başlayaraq)
    print("Yeni formullar əlavə edilir...")
    
    # Hər mağaza üçün formullar yaradırıq
    for row in range(4, 24):  # 4-cü sətirdən 23-cü sətrə qədər (20 mağaza)
        store_cell = f"$A{row}"
        
        # Satış formulları (B4:F23)
        payment_types_row3 = ["Nağd", "ABB", "Kapital", "M10", "Umico"]
        for col_idx, payment_type in enumerate(payment_types_row3, 2):
            col_letter = get_column_letter(col_idx)
            # Cədvəl adını istifadə etmədən adi aralıq istifadə edirik
            formula = f'=SUMIFS(\'✍️ Məlumatların Daxil Edilməsi\'.$E:$E, \'✍️ Məlumatların Daxil Edilməsi\'.$B:$B, {store_cell}, \'✍️ Məlumatların Daxil Edilməsi\'.$C:$C, "Satış", \'✍️ Məlumatların Daxil Edilməsi\'.$D:$D, {col_letter}$3)'
            ws_dashboard[f'{col_letter}{row}'] = formula
            ws_dashboard[f'{col_letter}{row}'].number_format = '#,##0.00 "₼"'
            ws_dashboard[f'{col_letter}{row}'].border = thin_border
        
        # Satış cəmi (G sütunu)
        ws_dashboard[f'G{row}'] = f'=SUM(B{row}:F{row})'
        ws_dashboard[f'G{row}'].fill = total_fill
        ws_dashboard[f'G{row}'].font = total_font
        ws_dashboard[f'G{row}'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'G{row}'].border = thin_border
        
        # Daxilolma formulları (H4:L23)
        for col_idx, payment_type in enumerate(payment_types_row3, 8):
            col_letter = get_column_letter(col_idx)
            formula = f'=SUMIFS(\'✍️ Məlumatların Daxil Edilməsi\'.$E:$E, \'✍️ Məlumatların Daxil Edilməsi\'.$B:$B, {store_cell}, \'✍️ Məlumatların Daxil Edilməsi\'.$C:$C, "Daxilolma", \'✍️ Məlumatların Daxil Edilməsi\'.$D:$D, {col_letter}$3)'
            ws_dashboard[f'{col_letter}{row}'] = formula
            ws_dashboard[f'{col_letter}{row}'].number_format = '#,##0.00 "₼"'
            ws_dashboard[f'{col_letter}{row}'].border = thin_border
        
        # Daxilolma cəmi (M sütunu)
        ws_dashboard[f'M{row}'] = f'=SUM(H{row}:L{row})'
        ws_dashboard[f'M{row}'].fill = total_fill
        ws_dashboard[f'M{row}'].font = total_font
        ws_dashboard[f'M{row}'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'M{row}'].border = thin_border
        
        # Xərc formulları (N4:R23)
        for col_idx, payment_type in enumerate(payment_types_row3, 14):
            col_letter = get_column_letter(col_idx)
            formula = f'=SUMIFS(\'✍️ Məlumatların Daxil Edilməsi\'.$E:$E, \'✍️ Məlumatların Daxil Edilməsi\'.$B:$B, {store_cell}, \'✍️ Məlumatların Daxil Edilməsi\'.$C:$C, "Xərc", \'✍️ Məlumatların Daxil Edilməsi\'.$D:$D, {col_letter}$3)'
            ws_dashboard[f'{col_letter}{row}'] = formula
            ws_dashboard[f'{col_letter}{row}'].number_format = '#,##0.00 "₼"'
            ws_dashboard[f'{col_letter}{row}'].border = thin_border
        
        # Xərc cəmi (S sütunu)
        ws_dashboard[f'S{row}'] = f'=SUM(N{row}:R{row})'
        ws_dashboard[f'S{row}'].fill = total_fill
        ws_dashboard[f'S{row}'].font = total_font
        ws_dashboard[f'S{row}'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'S{row}'].border = thin_border
        
        # Satış-Daxilolma Fərqi (T sütunu)
        ws_dashboard[f'T{row}'] = f'=G{row}-M{row}'
        ws_dashboard[f'T{row}'].fill = total_fill
        ws_dashboard[f'T{row}'].font = total_font
        ws_dashboard[f'T{row}'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'T{row}'].border = thin_border
        
        # Qalıq (U sütunu)
        ws_dashboard[f'U{row}'] = f'=M{row}-S{row}'
        ws_dashboard[f'U{row}'].fill = total_fill
        ws_dashboard[f'U{row}'].font = total_font
        ws_dashboard[f'U{row}'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'U{row}'].border = thin_border
    
    # Yekun sətir (24-cü sətir)
    ws_dashboard['A24'] = "YEKUN"
    ws_dashboard['A24'].font = Font(bold=True, color="FFFFFF")
    ws_dashboard['A24'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    
    # Yekun formulları
    for col in range(2, 22):  # B-dən U-ya qədər
        col_letter = get_column_letter(col)
        ws_dashboard[f'{col_letter}24'] = f'=SUM({col_letter}4:{col_letter}23)'
        ws_dashboard[f'{col_letter}24'].font = Font(bold=True, color="FFFFFF")
        ws_dashboard[f'{col_letter}24'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        ws_dashboard[f'{col_letter}24'].number_format = '#,##0.00 "₼"'
        ws_dashboard[f'{col_letter}24'].border = thin_border
    
    # Faylı yadda saxlayırıq
    wb.save(filename)
    print(f"Formullar düzəldildi: {filename}")
    
    print("\n" + "="*60)
    print("EXCEL FAYLI TAM HAZIRDIR!")
    print("="*60)
    print("Faylı açıb istifadə edə bilərsiniz.")
    print("Məlumatları '✍️ Məlumatların Daxil Edilməsi' səhifəsinə daxil edin.")
    print("Dashboard avtomatik yenilənəcək!")
    print("="*60)

if __name__ == "__main__":
    fix_formulas()
