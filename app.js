// Data structure
let appData = {
    suppliers: [],
    nextId: 1
};

// DOM Elements
const supplierContent = document.getElementById('supplierContent');
const totalDebtElement = document.getElementById('totalDebt');
const tabsContainer = document.querySelector('.tabs');
const addSupplierBtn = document.getElementById('addSupplierBtn');
const addTabBtn = document.getElementById('addTabBtn');
const supplierModal = document.getElementById('supplierModal');
const transactionModal = document.getElementById('transactionModal');
const supplierForm = document.getElementById('supplierForm');
const transactionForm = document.getElementById('transactionForm');
const exportBtn = document.getElementById('exportBtn');
const importBtn = document.getElementById('importBtn');

// Initialize the app
function initApp() {
    loadData();
    renderTabs();
    updateSummary();
    setupEventListeners();
    
    // Show summary tab by default
    showSummaryTab();
}

// Load data from localStorage
function loadData() {
    const savedData = localStorage.getItem('excelAppData');
    if (savedData) {
        appData = JSON.parse(savedData);
    }
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('excelAppData', JSON.stringify(appData));
}

// Setup event listeners
function setupEventListeners() {
    // Tab click events
    tabsContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('tab')) {
            const tabId = e.target.getAttribute('data-tab');
            activateTab(tabId);
        }
    });

    // Add supplier button
    addSupplierBtn.addEventListener('click', () => {
        supplierModal.style.display = 'block';
    });

    // Add tab button (same as add supplier)
    addTabBtn.addEventListener('click', () => {
        supplierModal.style.display = 'block';
    });

    // Close modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', () => {
            supplierModal.style.display = 'none';
            transactionModal.style.display = 'none';
        });
    });

    // Submit supplier form
    supplierForm.addEventListener('submit', (e) => {
        e.preventDefault();
        addSupplier();
    });

    // Submit transaction form
    transactionForm.addEventListener('submit', (e) => {
        e.preventDefault();
        addTransaction();
    });

    // Export button
    exportBtn.addEventListener('click', exportData);

    // Import button
    importBtn.addEventListener('click', () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = importData;
        input.click();
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === supplierModal) {
            supplierModal.style.display = 'none';
        }
        if (e.target === transactionModal) {
            transactionModal.style.display = 'none';
        }
    });
}

// Render tabs
function renderTabs() {
    // Clear existing supplier tabs
    const tabs = tabsContainer.querySelectorAll('.tab:not([data-tab="summary"])');
    tabs.forEach(tab => tab.remove());

    // Add summary tab if it doesn't exist
    if (!tabsContainer.querySelector('[data-tab="summary"]')) {
        const summaryTab = document.createElement('div');
        summaryTab.className = 'tab active';
        summaryTab.setAttribute('data-tab', 'summary');
        summaryTab.textContent = 'Ümumi';
        tabsContainer.appendChild(summaryTab);
    }

    // Add supplier tabs
    appData.suppliers.forEach(supplier => {
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.setAttribute('data-tab', supplier.id);
        tab.textContent = supplier.name;
        tabsContainer.appendChild(tab);
    });
}

// Activate tab
function activateTab(tabId) {
    // Update active tab
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.getAttribute('data-tab') === tabId) {
            tab.classList.add('active');
        }
    });

    // Show content based on tab
    if (tabId === 'summary') {
        showSummaryTab();
    } else {
        showSupplierTab(tabId);
    }
}

// Show summary tab
function showSummaryTab() {
    supplierContent.innerHTML = `
        <div class="supplier-header">
            <h2>Ümumi Borc Hesabatı</h2>
        </div>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Təchizatçı</th>
                    <th>Ümumi Borc</th>
                </tr>
            </thead>
            <tbody id="summaryTableBody">
                ${generateSummaryRows()}
            </tbody>
        </table>
    `;
}

// Generate summary rows
function generateSummaryRows() {
    let rows = '';
    let totalDebt = 0;

    appData.suppliers.forEach(supplier => {
        const supplierDebt = calculateSupplierDebt(supplier);
        totalDebt += supplierDebt;

        const debtClass = supplierDebt > 0 ? 'debt-positive' : 
                         supplierDebt < 0 ? 'debt-negative' : 'debt-zero';

        rows += `
            <tr>
                <td>${supplier.name}</td>
                <td class="${debtClass}">${supplierDebt.toFixed(2)} AZN</td>
            </tr>
        `;
    });

    // Add total row
    const totalDebtClass = totalDebt > 0 ? 'debt-positive' : 
                          totalDebt < 0 ? 'debt-negative' : 'debt-zero';
    
    rows += `
        <tr>
            <td>CƏMİ</td>
            <td class="${totalDebtClass}">${totalDebt.toFixed(2)} AZN</td>
        </tr>
    `;

    return rows;
}

// Show supplier tab
function showSupplierTab(supplierId) {
    const supplier = appData.suppliers.find(s => s.id.toString() === supplierId.toString());
    
    if (!supplier) {
        supplierContent.innerHTML = '<p>Təchizatçı tapılmadı.</p>';
        return;
    }

    const supplierDebt = calculateSupplierDebt(supplier);
    const debtClass = supplierDebt > 0 ? 'debt-positive' : 
                     supplierDebt < 0 ? 'debt-negative' : 'debt-zero';

    supplierContent.innerHTML = `
        <div class="supplier-header">
            <h2>${supplier.name}</h2>
            <button id="addTransactionBtn" data-supplier="${supplier.id}">
                <i class="fas fa-plus"></i> Yeni Əməliyyat
            </button>
        </div>
        
        <div class="supplier-summary">
            <h3>Ümumi Borc: <span class="${debtClass}">${supplierDebt.toFixed(2)} AZN</span></h3>
        </div>
        
        <table class="supplier-table">
            <thead>
                <tr>
                    <th>Tarix</th>
                    <th>Model Kodu</th>
                    <th>Say</th>
                    <th>Məbləğ</th>
                    <th>Növ</th>
                    <th>Qeyd</th>
                    <th>Əməliyyatlar</th>
                </tr>
            </thead>
            <tbody>
                ${generateTransactionRows(supplier)}
            </tbody>
        </table>
    `;

    // Add transaction button event
    document.getElementById('addTransactionBtn').addEventListener('click', () => {
        document.getElementById('supplierIdInput').value = supplier.id;
        document.getElementById('transactionDate').valueAsDate = new Date();
        transactionModal.style.display = 'block';
    });

    // Delete transaction buttons
    document.querySelectorAll('.delete-transaction').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const transactionId = e.target.getAttribute('data-id');
            deleteTransaction(supplier.id, transactionId);
        });
    });
}

// Generate transaction rows
function generateTransactionRows(supplier) {
    if (!supplier.transactions || supplier.transactions.length === 0) {
        return '<tr><td colspan="7" style="text-align: center;">Əməliyyat tapılmadı</td></tr>';
    }

    return supplier.transactions.map(transaction => {
        const type = transaction.type === 'debit' ? 'Borc' : 'Ödəniş';
        const amountClass = transaction.type === 'debit' ? 'debt-positive' : 'debt-negative';
        const amount = transaction.type === 'debit' ? 
            transaction.amount : 
            -transaction.amount;

        return `
            <tr>
                <td>${formatDate(transaction.date)}</td>
                <td>${transaction.modelCode || '-'}</td>
                <td>${transaction.quantity || '-'}</td>
                <td class="${amountClass}">${Math.abs(amount).toFixed(2)} AZN</td>
                <td>${type}</td>
                <td>${transaction.notes || '-'}</td>
                <td>
                    <button class="delete-transaction" data-id="${transaction.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// Add supplier
function addSupplier() {
    const supplierName = document.getElementById('supplierName').value.trim();
    
    if (!supplierName) {
        alert('Təchizatçı adı daxil edin!');
        return;
    }

    const newSupplier = {
        id: appData.nextId++,
        name: supplierName,
        transactions: []
    };

    appData.suppliers.push(newSupplier);
    saveData();
    renderTabs();
    
    // Reset and close form
    document.getElementById('supplierName').value = '';
    supplierModal.style.display = 'none';
    
    // Activate the new supplier tab
    activateTab(newSupplier.id.toString());
}

// Add transaction
function addTransaction() {
    const supplierId = document.getElementById('supplierIdInput').value;
    const date = document.getElementById('transactionDate').value;
    const modelCode = document.getElementById('modelCode').value.trim();
    const quantity = document.getElementById('quantity').value;
    const amount = parseFloat(document.getElementById('amount').value);
    const type = document.getElementById('transactionType').value;
    const notes = document.getElementById('notes').value.trim();

    if (!date || isNaN(amount)) {
        alert('Tarix və məbləğ daxil edin!');
        return;
    }

    const supplier = appData.suppliers.find(s => s.id.toString() === supplierId);
    
    if (!supplier) {
        alert('Təchizatçı tapılmadı!');
        return;
    }

    if (!supplier.transactions) {
        supplier.transactions = [];
    }

    const newTransaction = {
        id: Date.now(), // Use timestamp as unique ID
        date: date,
        modelCode: modelCode,
        quantity: quantity ? parseInt(quantity) : null,
        amount: amount,
        type: type,
        notes: notes
    };

    supplier.transactions.push(newTransaction);
    saveData();
    
    // Reset and close form
    transactionForm.reset();
    transactionModal.style.display = 'none';
    
    // Refresh the supplier tab
    showSupplierTab(supplierId);
    updateSummary();
}

// Delete transaction
function deleteTransaction(supplierId, transactionId) {
    if (!confirm('Bu əməliyyatı silmək istədiyinizə əminsiniz?')) {
        return;
    }

    const supplier = appData.suppliers.find(s => s.id.toString() === supplierId.toString());
    
    if (!supplier) {
        alert('Təchizatçı tapılmadı!');
        return;
    }

    supplier.transactions = supplier.transactions.filter(t => t.id.toString() !== transactionId.toString());
    saveData();
    
    // Refresh the supplier tab
    showSupplierTab(supplierId);
    updateSummary();
}

// Calculate supplier debt
function calculateSupplierDebt(supplier) {
    if (!supplier.transactions || supplier.transactions.length === 0) {
        return 0;
    }

    return supplier.transactions.reduce((total, transaction) => {
        if (transaction.type === 'debit') {
            return total + transaction.amount;
        } else {
            return total - transaction.amount;
        }
    }, 0);
}

// Update summary
function updateSummary() {
    let totalDebt = 0;
    
    appData.suppliers.forEach(supplier => {
        totalDebt += calculateSupplierDebt(supplier);
    });
    
    totalDebtElement.textContent = totalDebt.toFixed(2);
    
    // Update summary tab if it's active
    if (document.querySelector('.tab.active').getAttribute('data-tab') === 'summary') {
        showSummaryTab();
    }
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('az-AZ');
}

// Export data
function exportData() {
    const dataStr = JSON.stringify(appData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'techizcatci_data.json';
    link.click();
    
    URL.revokeObjectURL(url);
}

// Import data
function importData(event) {
    const file = event.target.files[0];
    
    if (!file) {
        return;
    }
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);
            
            if (!importedData.suppliers || !Array.isArray(importedData.suppliers)) {
                throw new Error('Yanlış data formatı');
            }
            
            if (confirm('Mövcud məlumatlar silinəcək. Davam etmək istəyirsiniz?')) {
                appData = importedData;
                saveData();
                renderTabs();
                updateSummary();
                showSummaryTab();
                alert('Məlumatlar uğurla import edildi!');
            }
        } catch (error) {
            alert('Fayl oxunarkən xəta baş verdi: ' + error.message);
        }
    };
    
    reader.readAsText(file);
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);
