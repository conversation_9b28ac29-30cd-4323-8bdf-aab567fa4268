<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Təchizatçı Sistemi</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="logo">Təchizatçı İdarəetmə Sistemi</div>
            <div class="toolbar">
                <button id="addSupplierBtn"><i class="fas fa-plus"></i> Yeni Təchizatçı</button>
                <button id="exportBtn"><i class="fas fa-file-export"></i> Export</button>
                <button id="importBtn"><i class="fas fa-file-import"></i> Import</button>
            </div>
        </div>
        
        <div class="main-content">
            <div id="supplierContent" class="supplier-content">
                <!-- Təchizatçı məlumatları burada göstəriləcək -->
                <div class="welcome-message">
                    <h2>Təchizatçı seçin və ya yeni təchizatçı əlavə edin</h2>
                    <p>Aşağıdakı tablardan bir təchizatçı seçin və ya yeni təchizatçı əlavə edin.</p>
                </div>
            </div>
            
            <div class="summary-panel">
                <h3>Ümumi Borc: <span id="totalDebt">0.00</span> AZN</h3>
            </div>
        </div>
        
        <div class="tabs-container">
            <div class="tabs">
                <div class="tab active" data-tab="summary">Ümumi</div>
                <!-- Təchizatçı tabları burada olacaq -->
            </div>
            <div class="tab-actions">
                <button id="addTabBtn"><i class="fas fa-plus"></i></button>
            </div>
        </div>
    </div>

    <!-- Modal for adding new supplier -->
    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Yeni Təchizatçı Əlavə Et</h2>
            <form id="supplierForm">
                <div class="form-group">
                    <label for="supplierName">Təchizatçı Adı:</label>
                    <input type="text" id="supplierName" required>
                </div>
                <button type="submit">Əlavə Et</button>
            </form>
        </div>
    </div>

    <!-- Modal for adding new transaction -->
    <div id="transactionModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Yeni Əməliyyat Əlavə Et</h2>
            <form id="transactionForm">
                <input type="hidden" id="supplierIdInput">
                <div class="form-group">
                    <label for="transactionDate">Tarix:</label>
                    <input type="date" id="transactionDate" required>
                </div>
                <div class="form-group">
                    <label for="modelCode">Model Kodu:</label>
                    <input type="text" id="modelCode">
                </div>
                <div class="form-group">
                    <label for="quantity">Say:</label>
                    <input type="number" id="quantity" min="0">
                </div>
                <div class="form-group">
                    <label for="amount">Məbləğ (AZN):</label>
                    <input type="number" id="amount" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="transactionType">Əməliyyat Növü:</label>
                    <select id="transactionType" required>
                        <option value="debit">Borc (Alış)</option>
                        <option value="credit">Ödəniş</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="notes">Qeyd:</label>
                    <textarea id="notes"></textarea>
                </div>
                <button type="submit">Əlavə Et</button>
            </form>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
